<%
// 优化后的座位配置数据 - 结构化和扁平化处理
const seatConfig = {
  columns: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K'],
  // 座位价格配置 - 根据座位类型和位置定义价格
  priceConfig: {
    // 基础价格配置
    basePrice: {
      'free': 0,        // 免费座位
      'paid': 200,      // 付费座位基础价格
      'emergency': 0, // 紧急出口座位
      'unreserved': 0,  // 未预留座位
      'not-available': 0, // 不可用座位
      'empty': 0        // 空位
    },

  },
  rows: [
    { number: 7, seats: { A: 'free', B: 'free', C: 'free', D: 'unreserved', E: 'free', F: 'unreserved', G: 'unreserved', H: 'free', J: 'free', K: 'free' } },
    { number: 8, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 9, seats: { A: 'paid', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 10, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 11, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 12, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 13, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'not-available', H: 'free', J: 'free', K: 'free' } },
    { number: 14, seats: { A: 'unreserved', B: 'unreserved', C: 'unreserved', D: 'unreserved', E: 'unreserved', F: 'not-available', G: 'not-available', H: 'unreserved', J: 'unreserved', K: 'unreserved' } },
    { number: 15, seats: { A: 'unreserved', B: 'unreserved', C: 'unreserved', D: 'unreserved', E: 'unreserved', F: 'unreserved', G: 'unreserved', H: 'unreserved', J: 'unreserved', K: 'unreserved' } },
    { number: 16, seats: { A: 'emergency', B: 'emergency', C: 'unreserved', D: 'unreserved', E: 'unreserved', F: 'unreserved', G: 'unreserved', H: 'unreserved', J: 'emergency', K: 'emergency' } },
    { number: 17, seats: { A: 'emergency', B: 'emergency', C: 'unreserved', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'emergency', K: 'emergency' } },
    { number: 18, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 19, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 20, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 21, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 22, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 23, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 24, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 25, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 26, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 27, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 28, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 29, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 30, seats: { A: 'free', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'free' } },
    { number: 31, seats: { A: 'empty', B: 'free', C: 'free', D: 'free', E: 'free', F: 'free', G: 'free', H: 'free', J: 'free', K: 'empty' } }
  ]
};
%>

<!-- Aircraft Seat Map -->
<div class="aircraft-seat-map">
  <div class="airline">
    <div class="seat-map-container">
      <!-- 飞机座位图 -->
      <div class="aircraft-seats">
        <!-- 座位区域 -->
        <div class="seats-container">
          <!-- 座位列标识 -->
          <div class="column-letters">
            <% seatConfig.columns.forEach((col, index) => { %>
            <div class="column-letter"><%= col %></div>
            <% if (index === 2) { %>
            <!-- 座位行标识 (左侧) - 在 C 和 D 中间 -->
            <div class="column-letter row-header"></div>
            <% } else if (index === 6) { %>
            <!-- 座位行标识 (右侧) - 在 G 和 H 中间 -->
            <div class="column-letter row-header"></div>
            <% } %>
            <% }); %>
          </div>

          <!-- 座位网格 - 优化后的直接渲染，无函数调用 -->
          <div class="seats-grid">
            <% seatConfig.rows.forEach(row => { %>
            <div class="seat-row" data-row="<%= row.number %>">
              <% seatConfig.columns.forEach((col, index) => { %>
              <% const seatStatus = row.seats[col]; %>
              <% if (seatStatus === 'empty') { %>
              <div class="seat-space" data-position="<%= row.number %><%= col %>"></div>
              <% } else { %>
              <%
              // 计算座位价格 - 仅使用基础价格
              let seatPrice = seatConfig.priceConfig.basePrice[seatStatus] || 0;
              %>
              <div
                class="seat seat-<%= seatStatus %>"
                data-row="<%= row.number %>"
                data-col="<%= col %>"
                data-seat="<%= row.number %><%= col %>"
                data-price="<%= seatPrice %>"
                data-currency="CNY"
                tabindex="0"
                role="button"
                aria-label="Seat <%= row.number %><%= col %> - <%= seatStatus %> - <%= seatPrice > 0 ? 'CNY ' + seatPrice : 'Free' %>"></div>
              <% } %>
              <% if (index === 2) { %>
              <div class="row-number left" aria-hidden="true"><%= row.number %></div>
              <% } else if (index === 6) { %>
              <div class="row-number right" aria-hidden="true"><%= row.number %></div>
              <% } %>
              <% }); %>
            </div>
            <% }); %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
