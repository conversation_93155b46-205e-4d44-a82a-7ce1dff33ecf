@import '../../../less/variables.less';
@import '../../../less/mediaMixin.less';

.additional-services-seat-card {
  .select-seat-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .seat-section {
    .seat-content {
      padding-bottom: 20px;

      .seat-notes {
        background: #fdf7e8;
        padding: 10px 20px;
        border: 1px solid #eeb71c;
        padding: 10px 20px;
        margin-bottom: 24px;

        .notes-content {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .notes-title {
          font-size: 16px;
          font-weight: 500;
          color: @gray-5;
          line-height: 23px;
        }

        .notes-text {
          font-size: 14px;
          font-weight: 400;
          color: @gray-5;
          line-height: 20px;
        }
      }

      .seat-selection-container {
        display: flex;
        gap: 20px;
        width: 100%;

        // Seat Legend Panel
        .seat-legend-panel {
          flex: 1;
          border: 1px solid #ccc;
          border-radius: 8px;
          overflow: hidden;
        }

        .seat-legend {
          padding: 10px 20px;

          .legend-row {
            display: flex;
            margin-bottom: 14px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .legend-item {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
            width: 170px;

            .legend-text {
              font-size: 16px;
              font-weight: 400;
              color: #3d3d3d;
              line-height: 23px;
            }
          }
        }

        // Seat Icons - Creating realistic seat shapes
        .seat-icon {
          width: 28px;
          height: 31px;
          position: relative;
          display: inline-block;

          // Armrests
          .armrest-left,
          .armrest-right {
            position: absolute;
            width: 2.5px;
            height: 7px;
            background: #e1e1e1;
            bottom: 8px;
          }

          .armrest-left {
            left: 10px;
            transform: rotate(-15deg);
          }

          .armrest-right {
            right: 10px;
            transform: rotate(15deg);
          }
        }

        .seat-free {
          background: url('../../images/additionalServices/free.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }

        .seat-unreserved {
          background: url('../../images/additionalServices/unreserved.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }

        .seat-not-available {
          background: url('../../images/additionalServices/not-available.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }

        .seat-emergency {
          background: url('../../images/additionalServices/emergency-exit.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }

        .seat-unadjustable {
          background: url('../../images/additionalServices/unadjustable.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }

        .seat-paid {
          background: url('../../images/additionalServices/paid.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }

        .seat-selected {
          background: url('../../images/additionalServices/selected.svg');
          background-repeat: no-repeat;
          background-size: contain;
        }

        .seat-highlight {
          box-shadow: 0 0 0 2px @brand-1;
          border-radius: 4px;
        }

        // Passenger Panel
        .passenger-panel {
          width: 454px;
          flex-shrink: 0;

          .panel-header {
            margin-bottom: 12px;

            .panel-title {
              font-size: 20px;
              font-weight: 400; // font_5:0648
              color: #101010; // paint_5:0184
              line-height: 29px;
              margin: 0;
            }
          }
        }

        .passenger-list {
          display: flex;
          flex-direction: column;
          gap: 14px;
          max-height: 800px;
          overflow-y: auto;

          // 优化滚动性能
          -webkit-overflow-scrolling: touch;
          scrollbar-width: thin;
          scrollbar-color: @brand-1 transparent;

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background: @brand-1;
            border-radius: 3px;
          }

          .loading-passengers,
          .no-passengers {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px 20px;
            font-size: 16px;
            color: @gray-4;
            text-align: center;
          }
        }

        .passenger-item {
          position: relative;
          display: flex;
          background: #f4f6f9; // paint_5:0311
          border-radius: 8px;
          border: 1px solid transparent;
          cursor: pointer;
          transition: all 0.3s ease;

          &.selected {
            background: #fff4f4;
            border: 1px solid @brand-1;
          }

          // 防止选中时的文本选择
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;

          .passenger-number {
            width: 28px;
            height: 28px;
            background: #b4aba4; // paint_5:0658
            border-radius: 8px 0 8px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500; // font_5:0555
            color: #ffffff; // paint_1:39
            position: absolute;
            top: -1px;
            left: -1px;
          }

          &.selected .passenger-number {
            background: @brand-1;
          }

          .passenger-content {
            flex: 1;
            padding: 10px 40px;
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
        }

        .passenger-info {
          .passenger-name {
            font-size: 20px;
            font-weight: 500; // font_1:51
            color: #4f3d1e; // paint_1:52
            line-height: 29px;
            margin-bottom: 4px;
          }

          .passenger-details {
            display: flex;
            align-items: center;
            gap: 8px;

            .passenger-badge {
              background: #fdf7e8; // paint_5:8997
              border: 1px solid #eeb71c; // paint_5:8988
              border-radius: 4px;
              padding: 1px 6px;
              font-size: 16px;
              font-weight: 400; // font_1:63
              color: #c47330; // paint_5:0304
              line-height: 23px;
            }

            .passenger-id {
              font-size: 16px;
              font-weight: 400; // font_1:63
              color: #c47330; // paint_5:0304
              line-height: 23px;
            }
          }
        }

        .seat-info {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .seat-display {
            display: flex;
            align-items: center;
            gap: 4px;

            .seat-icon-small {
              width: 24px;
              height: 24px;
              background: url('../../images/additionalServices/seat-icon-small.svg');
              background-size: contain;
            }

            .seat-number {
              font-size: 24px;
              font-weight: 500; // font_80:3240
              color: #942531; // paint_1:32
              line-height: 35px;
            }
          }

          .seat-price {
            font-size: 20px;
            font-weight: 500; // font_1:51
            color: #942531; // paint_1:32
            line-height: 29px;
          }

          .no-seat-selected {
            font-size: 20px;
            font-weight: 400; // font_5:0648
            color: #7a6a5e; // paint_5:0446
            line-height: 29px;
          }
        }

        // Aircraft Seat Map Styles - 优化性能和视觉效果
        .aircraft-seat-map {
          background: #404f66;
          margin: 0 auto;
          max-height: 750px;
          overflow-y: auto;
          border-radius: 8px;

          // 优化滚动性能
          -webkit-overflow-scrolling: touch;
          scrollbar-width: thin;
          scrollbar-color: rgba(255, 255, 255, 0.3) transparent;

          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: transparent;
          }

          &::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
          }

          .airline {
            width: 100%;
            height: 2593px;
            background: url('../../images/additionalServices/airplane-background.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center top;
            padding-top: 500px;

            // 启用硬件加速
            will-change: transform;

            .aircraft-seats {
              display: flex;
              justify-content: center;
              gap: 10px;
            }

            .row-number {
              width: 35px;
              height: 38px;
              display: flex;
              align-items: center;
              justify-content: center;

              font-size: 12px;
              color: @gray-0;
            }

            .seats-container {
              display: flex;
              flex-direction: column;
              gap: 10px;

              .column-letters {
                display: flex;
                justify-content: center;
                gap: 5px;
                margin-bottom: 10px;

                .column-letter {
                  width: 35px;
                  text-align: center;
                  font-size: 16px;
                  color: @gray-0;

                  &.empty {
                    width: 20px;
                  }
                }
              }

              .seats-grid {
                display: flex;
                flex-direction: column;
                gap: 12px;

                .seat-row {
                  display: flex;
                  justify-content: center;
                  gap: 5px;

                  .seat {
                    width: 35px;
                    height: 38px;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &.seat-not-available,
                    &.seat-emergency,
                    &.seat-unreserved {
                      cursor: not-allowed;
                      opacity: 0.7;
                    }
                  }

                  .seat-space {
                    width: 35px;
                    height: 38px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .boarding-services {
    background: #fdf7e8;
    border: 1px solid #eeb71c;
    padding: 10px 20px;
    margin: 30px 0;

    &-title {
      margin-bottom: 10px;

      font-size: 20px;
      font-weight: 500;
      color: @gray-5;
    }

    &-text {
      font-size: 16px;
      color: @gray-5;
    }
  }

  .button-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin-top: 20px;

    .btn-skip,
    .btn-confirm {
      width: 230px;
      height: 52px;
      font-size: 20px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .btn-skip {
      background: #ffffff; // 中性色/gray-0
      border: 2px solid @brand-1;
      color: @brand-1;

      &:hover {
        background: #f5f5f5;
      }
    }

    .btn-confirm {
      background: @brand-1;
      border: 2px solid @brand-1;
      color: #ffffff; // 中性色/gray-0

      &:hover {
        background: #a00100;
        border-color: #a00100;
      }
    }
  }
}
