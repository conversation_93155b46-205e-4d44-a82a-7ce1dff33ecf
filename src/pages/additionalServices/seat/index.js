/**
 * 座位选择管理类
 * 每个航段独立管理座位图和乘客状态
 */
class SeatSelectionManager {
  constructor() {
    // 存储每个航段的座位选择状态
    this.segmentData = {};
    // 存储每个航段的当前选中乘客
    this.currentPassengers = {};
    // 存储 Tippy 实例
    this.tippyInstances = [];

    this.init();
  }

  /**
   * 初始化
   */
  init() {
    this.initializeSegmentData();
    this.bindEvents();
    this.initSeatTooltips();
  }

  /**
   * 初始化航段数据
   */
  initializeSegmentData() {
    $('.seat-section').each((_, element) => {
      const $section = $(element);
      const segmentIndex = parseInt($section.attr('id').replace('seat-section-', ''));

      // 初始化该航段的数据结构
      this.segmentData[segmentIndex] = {
        passengerSeats: {}, // 乘客索引 -> 座位号
        seatPassengers: {}, // 座位号 -> 乘客索引
      };

      // 从DOM中读取已选座位信息
      this.loadExistingSeats(segmentIndex);

      // 默认选择第一个旅客
      this.setDefaultPassenger(segmentIndex);
    });
  }

  /**
   * 从DOM加载已存在的座位选择
   */
  loadExistingSeats(segmentIndex) {
    const segmentData = this.segmentData[segmentIndex];
    const $contentDiv = $(`#seat-content-${segmentIndex}`);

    $contentDiv.find('.passenger-item').each((passengerIndex, element) => {
      const $passenger = $(element);
      const $seatNumber = $passenger.find('.seat-number');

      if ($seatNumber.length > 0) {
        const seatId = $seatNumber.text().trim();
        if (seatId) {
          segmentData.passengerSeats[passengerIndex] = seatId;
          segmentData.seatPassengers[seatId] = passengerIndex;

          // 在座位图中标记为已选中
          this.markSeatAsSelectedInContent(segmentIndex, seatId);

          // 更新价格显示
          this.updatePassengerSeatDisplay(segmentIndex, passengerIndex, seatId, $contentDiv);
        }
      }
    });
  }

  /**
   * 设置默认选中的旅客（第一个旅客）
   */
  setDefaultPassenger(segmentIndex) {
    const $contentDiv = $(`#seat-content-${segmentIndex}`);
    const $firstPassenger = $contentDiv.find('.passenger-item').first();

    if ($firstPassenger.length > 0) {
      // 设置第一个旅客为默认选中
      this.currentPassengers[segmentIndex] = 0;
    }
  }

  /**
   * 在原始内容中标记座位为已选中
   */
  markSeatAsSelectedInContent(segmentIndex, seatId) {
    const $contentDiv = $(`#seat-content-${segmentIndex}`);
    const $seat = $contentDiv.find(`.seat[data-seat="${seatId}"]`);
    if ($seat.length > 0) {
      // 移除所有状态类，添加选中状态
      $seat
        .removeClass('seat-free seat-paid seat-emergency seat-unreserved seat-not-available')
        .addClass('seat-selected');
    }
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听展开控制按钮点击，在展开后初始化内容
    $(document).on('click', '.expand-control', e => {
      const $expandControl = $(e.currentTarget);
      const $flightCard = $expandControl.closest('.flight-info-card');
      const $expandContent = $flightCard.find('.expand-content');

      // 延迟执行，等待expand-card完成内容复制
      setTimeout(() => {
        if ($flightCard.hasClass('expanded') && $expandContent.is(':visible')) {
          this.initializeExpandedContent($expandContent);
        }
      }, 250);
    });

    // 乘客选择事件 - 处理每个航段独立的乘客选择
    $(document).on('click', '.passenger-item', e => {
      const $passenger = $(e.currentTarget);
      const passengerIndex = parseInt($passenger.data('passenger-index'));

      // 找到对应的航段容器
      const $container = $passenger.closest('.expand-content');
      if ($container.length === 0 || !$container.is(':visible')) {
        return;
      }

      // 获取航段索引
      const $section = $container.closest('.seat-section');
      const segmentIndex = parseInt($section.attr('id').replace('seat-section-', ''));

      this.selectPassenger(segmentIndex, passengerIndex, $container);
    });

    // 座位选择事件 - 处理每个航段独立的座位选择
    $(document).on('click', '.seat', e => {
      const $seat = $(e.currentTarget);
      const seatId = $seat.data('seat');

      // 找到座位所属的航段容器
      const $container = $seat.closest('.expand-content');
      if ($container.length === 0 || !$container.is(':visible')) {
        return;
      }

      // 获取航段索引
      const $section = $container.closest('.seat-section');
      const segmentIndex = parseInt($section.attr('id').replace('seat-section-', ''));

      // 检查该航段是否有选中的乘客
      if (this.currentPassengers[segmentIndex] === null) {
        return;
      }

      this.selectSeat(segmentIndex, seatId, $container);
    });

    // 跳过按钮事件
    $('.btn-skip').on('click', () => {
      this.handleSkip();
    });

    // 确认按钮事件
    $('.btn-confirm').on('click', () => {
      this.handleConfirm();
    });
  }

  /**
   * 初始化展开内容
   */
  initializeExpandedContent($expandContent) {
    // 获取航段索引
    const $section = $expandContent.closest('.seat-section');
    const segmentIndex = parseInt($section.attr('id').replace('seat-section-', ''));

    // 默认选中第一个旅客
    const defaultPassengerIndex = this.currentPassengers[segmentIndex];
    if (defaultPassengerIndex !== null) {
      const $firstPassenger = $expandContent.find(`.passenger-item[data-passenger-index="${defaultPassengerIndex}"]`);
      $firstPassenger.addClass('selected');

      // 显示该航段的已选座位和高亮
      this.restoreSegmentSeatsInContainer(segmentIndex, $expandContent);
      this.updateSeatHighlight(segmentIndex, defaultPassengerIndex, $expandContent);

      // 重新初始化该容器中的座位提示
      this.initSeatTooltipsInContainer($expandContent);
    }
  }

  /**
   * 在容器中恢复航段的座位选择状态
   */
  restoreSegmentSeatsInContainer(segmentIndex, $container) {
    const segmentData = this.segmentData[segmentIndex];
    if (!segmentData) return;

    // 恢复所有已选座位的显示和价格
    Object.keys(segmentData.seatPassengers).forEach(seatId => {
      const passengerIndex = segmentData.seatPassengers[seatId];
      this.markSeatAsSelected(seatId, $container);
      // 更新价格显示
      this.updatePassengerSeatDisplay(segmentIndex, passengerIndex, seatId, $container);
    });
  }

  /**
   * 选择乘客
   */
  selectPassenger(segmentIndex, passengerIndex, $container) {
    // 清除该航段内所有乘客的选中状态
    $container.find('.passenger-item').removeClass('selected');

    // 选中当前乘客
    const $selectedPassenger = $container.find(`.passenger-item[data-passenger-index="${passengerIndex}"]`);
    $selectedPassenger.addClass('selected');

    // 设置该航段的当前选中乘客
    this.currentPassengers[segmentIndex] = passengerIndex;

    // 更新该航段座位图的高亮显示
    this.updateSeatHighlight(segmentIndex, passengerIndex, $container);
  }

  /**
   * 选择座位
   */
  selectSeat(segmentIndex, seatId, $container) {
    const currentPassengerIndex = this.currentPassengers[segmentIndex];
    if (currentPassengerIndex === null) {
      return;
    }

    // 在该航段容器中找到座位
    const $seat = $container.find(`.seat[data-seat="${seatId}"]`);
    if ($seat.length === 0) {
      return;
    }

    // 检查座位是否可选
    if (!this.isSeatSelectable($seat)) {
      return;
    }

    const segmentData = this.segmentData[segmentIndex];

    // 检查座位是否已被其他乘客选择
    if (segmentData.seatPassengers[seatId] && segmentData.seatPassengers[seatId] !== currentPassengerIndex) {
      return;
    }

    // 清除该乘客之前的座位选择
    const previousSeat = segmentData.passengerSeats[currentPassengerIndex];
    if (previousSeat) {
      delete segmentData.seatPassengers[previousSeat];
      this.clearSeatSelection(previousSeat, $container);
    }

    // 设置新的座位选择
    segmentData.passengerSeats[currentPassengerIndex] = seatId;
    segmentData.seatPassengers[seatId] = currentPassengerIndex;

    // 更新UI显示
    this.updatePassengerSeatDisplay(segmentIndex, currentPassengerIndex, seatId, $container);
    this.markSeatAsSelected(seatId, $container);

    // 更新座位高亮
    this.updateSeatHighlight(segmentIndex, currentPassengerIndex, $container);
  }

  /**
   * 检查座位是否可选
   */
  isSeatSelectable($seat) {
    const seatClasses = $seat.attr('class');
    return seatClasses.includes('seat-free') || seatClasses.includes('seat-paid');
  }

  /**
   * 标记座位为已选中
   */
  markSeatAsSelected(seatId, $container) {
    const $seat = $container ? $container.find(`.seat[data-seat="${seatId}"]`) : $(`.seat[data-seat="${seatId}"]`);
    // 移除所有状态类，添加选中状态
    $seat
      .removeClass('seat-free seat-paid seat-emergency seat-unreserved seat-not-available')
      .addClass('seat-selected');
  }

  /**
   * 清除座位选择状态
   */
  clearSeatSelection(seatId, $container) {
    const $seat = $container ? $container.find(`.seat[data-seat="${seatId}"]`) : $(`.seat[data-seat="${seatId}"]`);

    // 移除选中状态
    $seat.removeClass('seat-selected');

    // 恢复到原始状态
    const originalStatus = $seat.data('original-status');
    if (originalStatus) {
      // 移除所有状态类
      $seat.removeClass('seat-free seat-paid seat-emergency seat-unreserved seat-not-available');
      // 添加原始状态类
      $seat.addClass(`seat-${originalStatus}`);
    }
  }

  /**
   * 获取座位价格 - 从HTML数据属性中读取
   */
  getSeatPrice(seatId) {
    const $seat = $(`.seat[data-seat="${seatId}"]`);

    // 从数据属性中读取价格
    const priceData = $seat.data('price');
    const currencyData = $seat.data('currency');

    if (priceData !== undefined && currencyData) {
      return {
        currency: currencyData,
        amount: parseInt(priceData) || 0,
      };
    }

    return { currency: 'CNY', amount: 0 };
  }

  /**
   * 更新乘客座位显示
   */
  updatePassengerSeatDisplay(segmentIndex, passengerIndex, seatId, $container) {
    // 在当前容器中查找乘客
    const $passenger = $container.find(`.passenger-item[data-passenger-index="${passengerIndex}"]`);
    const $seatInfo = $passenger.find('.seat-info');

    // 同时更新原始隐藏内容中的数据
    const $originalContent = $(`#seat-content-${segmentIndex}`);
    const $originalPassenger = $originalContent.find(`.passenger-item[data-passenger-index="${passengerIndex}"]`);
    const $originalSeatInfo = $originalPassenger.find('.seat-info');

    if (seatId) {
      const priceInfo = this.getSeatPrice(seatId);
      const priceDisplay =
        priceInfo.amount > 0 ? `<div class="seat-price">${priceInfo.currency} ${priceInfo.amount}</div>` : '';

      const seatHtml = `
        <div class="seat-display">
          <div class="seat-icon-small"></div>
          <div class="seat-number">${seatId}</div>
        </div>
        ${priceDisplay}
      `;

      // 更新展开内容中的显示
      $seatInfo.html(seatHtml);
      // 更新原始内容中的数据
      $originalSeatInfo.html(seatHtml);
    } else {
      const noSeatHtml = '<div class="no-seat-selected">No seat selected</div>';
      // 清除座位选择
      $seatInfo.html(noSeatHtml);
      $originalSeatInfo.html(noSeatHtml);
    }
  }

  /**
   * 更新座位高亮显示
   */
  updateSeatHighlight(segmentIndex, passengerIndex, $container) {
    // 清除该容器内所有座位的高亮
    $container.find('.seat').removeClass('seat-highlight');

    const segmentData = this.segmentData[segmentIndex];
    if (!segmentData) return;

    const currentSeat = segmentData.passengerSeats[passengerIndex];

    // 高亮当前乘客已选的座位
    if (currentSeat) {
      $container.find(`.seat[data-seat="${currentSeat}"]`).addClass('seat-highlight');
    }
  }

  /**
   * 处理跳过按钮点击
   */
  handleSkip() {
    // 这里可以添加跳过逻辑，比如导航到下一页
    console.log('跳过座位选择');
    // 示例：window.location.href = '/next-step';
  }

  /**
   * 处理确认按钮点击
   */
  handleConfirm() {
    // 收集所有航段的座位选择数据
    const allSelections = this.getAllSelections();

    console.log('确认座位选择:', allSelections);

    // 这里可以添加提交逻辑
    // 示例：this.submitSelections(allSelections);
  }

  /**
   * 获取所有航段的选择数据
   */
  getAllSelections() {
    const selections = {};

    Object.keys(this.segmentData).forEach(segmentIndex => {
      const data = this.segmentData[segmentIndex];
      selections[segmentIndex] = {
        passengers: {},
      };

      Object.keys(data.passengerSeats).forEach(passengerIndex => {
        const seatId = data.passengerSeats[passengerIndex];
        const priceInfo = this.getSeatPrice(seatId);
        selections[segmentIndex].passengers[passengerIndex] = {
          seatId: seatId,
          price: priceInfo.amount,
          currency: priceInfo.currency,
        };
      });
    });

    return selections;
  }

  /**
   * 初始化座位提示
   */
  initSeatTooltips() {
    // 销毁现有的 Tippy 实例
    this.destroySeatTooltips();

    // 为所有座位元素创建提示
    const seatElements = document.querySelectorAll('.seat:not(.seat-space)');

    seatElements.forEach(seatElement => {
      const seatId = seatElement.getAttribute('data-seat');
      const priceData = seatElement.getAttribute('data-price');
      const currencyData = seatElement.getAttribute('data-currency');
      const originalStatus = seatElement.getAttribute('data-original-status');

      // 生成提示内容
      const tooltipContent = this.generateSeatTooltipContent(seatId, priceData, currencyData, originalStatus);

      // 创建 Tippy 实例
      const tippyInstance = tippy(seatElement, {
        content: tooltipContent,
        allowHTML: true,
        placement: 'top',
        theme: 'seat-tooltip',
        arrow: true,
        delay: [200, 0],
        duration: [200, 150],
        maxWidth: 200,
        interactive: false,
        hideOnClick: false,
      });

      this.tippyInstances.push(tippyInstance);
    });
  }

  /**
   * 生成座位提示内容
   */
  generateSeatTooltipContent(seatId, priceData, currencyData, originalStatus) {
    const price = parseInt(priceData) || 0;
    const currency = currencyData || 'CNY';

    // 根据座位状态生成标题
    let statusTitle = '';
    switch (originalStatus) {
      case 'free':
        statusTitle = 'Free seats';
        break;
      case 'paid':
        statusTitle = 'Premium seats';
        break;
      case 'emergency':
        statusTitle = 'Emergency exit';
        break;
      case 'unreserved':
        statusTitle = 'Unreserved';
        break;
      case 'not-available':
        statusTitle = 'Not available';
        break;
      default:
        statusTitle = 'Seat';
    }

    // 按照要求的格式生成内容：座位号 + 状态标题 + 价格（如果有）
    let content = `
      <div class="seat-tooltip-content">
        <div class="tooltip-seat-id">${seatId}</div>
        <div class="tooltip-status">${statusTitle}</div>
    `;

    // 如果有价格，添加价格显示（红色）
    if (price > 0) {
      content += `<div class="tooltip-price">${currency} ${price}</div>`;
    }

    content += `</div>`;

    return content;
  }

  /**
   * 为特定容器中的座位初始化提示
   */
  initSeatTooltipsInContainer($container) {
    const seatElements = $container.find('.seat:not(.seat-space)');

    seatElements.each((_, seatElement) => {
      const seatId = seatElement.getAttribute('data-seat');
      const priceData = seatElement.getAttribute('data-price');
      const currencyData = seatElement.getAttribute('data-currency');
      const originalStatus = seatElement.getAttribute('data-original-status');

      // 生成提示内容
      const tooltipContent = this.generateSeatTooltipContent(seatId, priceData, currencyData, originalStatus);

      // 创建 Tippy 实例
      const tippyInstance = tippy(seatElement, {
        content: tooltipContent,
        allowHTML: true,
        placement: 'top',
        theme: 'seat-tooltip',
        arrow: true,
        delay: [200, 0],
        duration: [200, 150],
        maxWidth: 200,
        interactive: false,
        hideOnClick: false,
      });

      this.tippyInstances.push(tippyInstance);
    });
  }

  /**
   * 销毁座位提示
   */
  destroySeatTooltips() {
    this.tippyInstances.forEach(instance => {
      if (instance && instance.destroy) {
        instance.destroy();
      }
    });
    this.tippyInstances = [];
  }
}

// 初始化座位选择管理器
$(document).ready(() => {
  new SeatSelectionManager();
});
